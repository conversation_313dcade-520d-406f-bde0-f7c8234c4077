{"version": 3, "file": "ColumnSizing.js", "sources": ["../../../src/features/ColumnSizing.ts"], "sourcesContent": ["import { _getVisibleLeafColumns } from '..'\nimport {\n  <PERSON><PERSON><PERSON>,\n  <PERSON>umn,\n  <PERSON>er,\n  OnChangeFn,\n  Table,\n  Updater,\n  TableFeature,\n} from '../types'\nimport { getMemoOptions, makeStateUpdater, memo } from '../utils'\nimport { ColumnPinningPosition } from './ColumnPinning'\nimport { safelyAccessDocument } from '../utils/document'\n\n//\n\nexport interface ColumnSizingTableState {\n  columnSizing: ColumnSizingState\n  columnSizingInfo: ColumnSizingInfoState\n}\n\nexport type ColumnSizingState = Record<string, number>\n\nexport interface ColumnSizingInfoState {\n  columnSizingStart: [string, number][]\n  deltaOffset: null | number\n  deltaPercentage: null | number\n  isResizingColumn: false | string\n  startOffset: null | number\n  startSize: null | number\n}\n\nexport type ColumnResizeMode = 'onChange' | 'onEnd'\n\nexport type ColumnResizeDirection = 'ltr' | 'rtl'\n\nexport interface ColumnSizingOptions {\n  /**\n   * Determines when the columnSizing state is updated. `onChange` updates the state when the user is dragging the resize handle. `onEnd` updates the state when the user releases the resize handle.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#columnresizemode)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  columnResizeMode?: ColumnResizeMode\n  /**\n   * Enables or disables column resizing for the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#enablecolumnresizing)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  enableColumnResizing?: boolean\n  /**\n   * Enables or disables right-to-left support for resizing the column. defaults to 'ltr'.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#columnResizeDirection)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  columnResizeDirection?: ColumnResizeDirection\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.columnSizing` changes. This overrides the default internal state management, so you will also need to supply `state.columnSizing` from your own managed state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#oncolumnsizingchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  onColumnSizingChange?: OnChangeFn<ColumnSizingState>\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.columnSizingInfo` changes. This overrides the default internal state management, so you will also need to supply `state.columnSizingInfo` from your own managed state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#oncolumnsizinginfochange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  onColumnSizingInfoChange?: OnChangeFn<ColumnSizingInfoState>\n}\n\nexport type ColumnSizingDefaultOptions = Pick<\n  ColumnSizingOptions,\n  | 'columnResizeMode'\n  | 'onColumnSizingChange'\n  | 'onColumnSizingInfoChange'\n  | 'columnResizeDirection'\n>\n\nexport interface ColumnSizingInstance {\n  /**\n   * If pinning, returns the total size of the center portion of the table by calculating the sum of the sizes of all unpinned/center leaf-columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getcentertotalsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getCenterTotalSize: () => number\n  /**\n   * Returns the total size of the left portion of the table by calculating the sum of the sizes of all left leaf-columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getlefttotalsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getLeftTotalSize: () => number\n  /**\n   * Returns the total size of the right portion of the table by calculating the sum of the sizes of all right leaf-columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getrighttotalsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getRightTotalSize: () => number\n  /**\n   * Returns the total size of the table by calculating the sum of the sizes of all leaf-columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#gettotalsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getTotalSize: () => number\n  /**\n   * Resets column sizing to its initial state. If `defaultState` is `true`, the default state for the table will be used instead of the initialValue provided to the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#resetcolumnsizing)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  resetColumnSizing: (defaultState?: boolean) => void\n  /**\n   * Resets column sizing info to its initial state. If `defaultState` is `true`, the default state for the table will be used instead of the initialValue provided to the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#resetheadersizeinfo)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  resetHeaderSizeInfo: (defaultState?: boolean) => void\n  /**\n   * Sets the column sizing state using an updater function or a value. This will trigger the underlying `onColumnSizingChange` function if one is passed to the table options, otherwise the state will be managed automatically by the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#setcolumnsizing)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  setColumnSizing: (updater: Updater<ColumnSizingState>) => void\n  /**\n   * Sets the column sizing info state using an updater function or a value. This will trigger the underlying `onColumnSizingInfoChange` function if one is passed to the table options, otherwise the state will be managed automatically by the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#setcolumnsizinginfo)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  setColumnSizingInfo: (updater: Updater<ColumnSizingInfoState>) => void\n}\n\nexport interface ColumnSizingColumnDef {\n  /**\n   * Enables or disables column resizing for the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#enableresizing)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  enableResizing?: boolean\n  /**\n   * The maximum allowed size for the column\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#maxsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  maxSize?: number\n  /**\n   * The minimum allowed size for the column\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#minsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  minSize?: number\n  /**\n   * The desired size for the column\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#size)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  size?: number\n}\n\nexport interface ColumnSizingColumn {\n  /**\n   * Returns `true` if the column can be resized.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getcanresize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getCanResize: () => boolean\n  /**\n   * Returns `true` if the column is currently being resized.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getisresizing)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getIsResizing: () => boolean\n  /**\n   * Returns the current size of the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getSize: () => number\n  /**\n   * Returns the offset measurement along the row-axis (usually the x-axis for standard tables) for the header. This is effectively a sum of the offset measurements of all preceding (left) headers in relation to the current column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getstart)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getStart: (position?: ColumnPinningPosition | 'center') => number\n  /**\n   * Returns the offset measurement along the row-axis (usually the x-axis for standard tables) for the header. This is effectively a sum of the offset measurements of all succeeding (right) headers in relation to the current column.\n   */\n  getAfter: (position?: ColumnPinningPosition | 'center') => number\n  /**\n   * Resets the column to its initial size.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#resetsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  resetSize: () => void\n}\n\nexport interface ColumnSizingHeader {\n  /**\n   * Returns an event handler function that can be used to resize the header. It can be used as an:\n   * - `onMouseDown` handler\n   * - `onTouchStart` handler\n   *\n   * The dragging and release events are automatically handled for you.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getresizehandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getResizeHandler: (context?: Document) => (event: unknown) => void\n  /**\n   * Returns the current size of the header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getSize: () => number\n  /**\n   * Returns the offset measurement along the row-axis (usually the x-axis for standard tables) for the header. This is effectively a sum of the offset measurements of all preceding headers.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getstart)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getStart: (position?: ColumnPinningPosition) => number\n}\n\n//\n\nexport const defaultColumnSizing = {\n  size: 150,\n  minSize: 20,\n  maxSize: Number.MAX_SAFE_INTEGER,\n}\n\nconst getDefaultColumnSizingInfoState = (): ColumnSizingInfoState => ({\n  startOffset: null,\n  startSize: null,\n  deltaOffset: null,\n  deltaPercentage: null,\n  isResizingColumn: false,\n  columnSizingStart: [],\n})\n\nexport const ColumnSizing: TableFeature = {\n  getDefaultColumnDef: (): ColumnSizingColumnDef => {\n    return defaultColumnSizing\n  },\n  getInitialState: (state): ColumnSizingTableState => {\n    return {\n      columnSizing: {},\n      columnSizingInfo: getDefaultColumnSizingInfoState(),\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): ColumnSizingDefaultOptions => {\n    return {\n      columnResizeMode: 'onEnd',\n      columnResizeDirection: 'ltr',\n      onColumnSizingChange: makeStateUpdater('columnSizing', table),\n      onColumnSizingInfoChange: makeStateUpdater('columnSizingInfo', table),\n    }\n  },\n\n  createColumn: <TData extends RowData, TValue>(\n    column: Column<TData, TValue>,\n    table: Table<TData>\n  ): void => {\n    column.getSize = () => {\n      const columnSize = table.getState().columnSizing[column.id]\n\n      return Math.min(\n        Math.max(\n          column.columnDef.minSize ?? defaultColumnSizing.minSize,\n          columnSize ?? column.columnDef.size ?? defaultColumnSizing.size\n        ),\n        column.columnDef.maxSize ?? defaultColumnSizing.maxSize\n      )\n    }\n\n    column.getStart = memo(\n      position => [\n        position,\n        _getVisibleLeafColumns(table, position),\n        table.getState().columnSizing,\n      ],\n      (position, columns) =>\n        columns\n          .slice(0, column.getIndex(position))\n          .reduce((sum, column) => sum + column.getSize(), 0),\n      getMemoOptions(table.options, 'debugColumns', 'getStart')\n    )\n\n    column.getAfter = memo(\n      position => [\n        position,\n        _getVisibleLeafColumns(table, position),\n        table.getState().columnSizing,\n      ],\n      (position, columns) =>\n        columns\n          .slice(column.getIndex(position) + 1)\n          .reduce((sum, column) => sum + column.getSize(), 0),\n      getMemoOptions(table.options, 'debugColumns', 'getAfter')\n    )\n\n    column.resetSize = () => {\n      table.setColumnSizing(({ [column.id]: _, ...rest }) => {\n        return rest\n      })\n    }\n    column.getCanResize = () => {\n      return (\n        (column.columnDef.enableResizing ?? true) &&\n        (table.options.enableColumnResizing ?? true)\n      )\n    }\n    column.getIsResizing = () => {\n      return table.getState().columnSizingInfo.isResizingColumn === column.id\n    }\n  },\n\n  createHeader: <TData extends RowData, TValue>(\n    header: Header<TData, TValue>,\n    table: Table<TData>\n  ): void => {\n    header.getSize = () => {\n      let sum = 0\n\n      const recurse = (header: Header<TData, TValue>) => {\n        if (header.subHeaders.length) {\n          header.subHeaders.forEach(recurse)\n        } else {\n          sum += header.column.getSize() ?? 0\n        }\n      }\n\n      recurse(header)\n\n      return sum\n    }\n    header.getStart = () => {\n      if (header.index > 0) {\n        const prevSiblingHeader = header.headerGroup.headers[header.index - 1]!\n        return prevSiblingHeader.getStart() + prevSiblingHeader.getSize()\n      }\n\n      return 0\n    }\n    header.getResizeHandler = _contextDocument => {\n      const column = table.getColumn(header.column.id)\n      const canResize = column?.getCanResize()\n\n      return (e: unknown) => {\n        if (!column || !canResize) {\n          return\n        }\n\n        ;(e as any).persist?.()\n\n        if (isTouchStartEvent(e)) {\n          // lets not respond to multiple touches (e.g. 2 or 3 fingers)\n          if (e.touches && e.touches.length > 1) {\n            return\n          }\n        }\n\n        const startSize = header.getSize()\n\n        const columnSizingStart: [string, number][] = header\n          ? header.getLeafHeaders().map(d => [d.column.id, d.column.getSize()])\n          : [[column.id, column.getSize()]]\n\n        const clientX = isTouchStartEvent(e)\n          ? Math.round(e.touches[0]!.clientX)\n          : (e as MouseEvent).clientX\n\n        const newColumnSizing: ColumnSizingState = {}\n\n        const updateOffset = (\n          eventType: 'move' | 'end',\n          clientXPos?: number\n        ) => {\n          if (typeof clientXPos !== 'number') {\n            return\n          }\n\n          table.setColumnSizingInfo(old => {\n            const deltaDirection =\n              table.options.columnResizeDirection === 'rtl' ? -1 : 1\n            const deltaOffset =\n              (clientXPos - (old?.startOffset ?? 0)) * deltaDirection\n            const deltaPercentage = Math.max(\n              deltaOffset / (old?.startSize ?? 0),\n              -0.999999\n            )\n\n            old.columnSizingStart.forEach(([columnId, headerSize]) => {\n              newColumnSizing[columnId] =\n                Math.round(\n                  Math.max(headerSize + headerSize * deltaPercentage, 0) * 100\n                ) / 100\n            })\n\n            return {\n              ...old,\n              deltaOffset,\n              deltaPercentage,\n            }\n          })\n\n          if (\n            table.options.columnResizeMode === 'onChange' ||\n            eventType === 'end'\n          ) {\n            table.setColumnSizing(old => ({\n              ...old,\n              ...newColumnSizing,\n            }))\n          }\n        }\n\n        const onMove = (clientXPos?: number) => updateOffset('move', clientXPos)\n\n        const onEnd = (clientXPos?: number) => {\n          updateOffset('end', clientXPos)\n\n          table.setColumnSizingInfo(old => ({\n            ...old,\n            isResizingColumn: false,\n            startOffset: null,\n            startSize: null,\n            deltaOffset: null,\n            deltaPercentage: null,\n            columnSizingStart: [],\n          }))\n        }\n\n        const contextDocument = safelyAccessDocument(_contextDocument)\n\n        const mouseEvents = {\n          moveHandler: (e: MouseEvent) => onMove(e.clientX),\n          upHandler: (e: MouseEvent) => {\n            contextDocument?.removeEventListener(\n              'mousemove',\n              mouseEvents.moveHandler\n            )\n            contextDocument?.removeEventListener(\n              'mouseup',\n              mouseEvents.upHandler\n            )\n            onEnd(e.clientX)\n          },\n        }\n\n        const touchEvents = {\n          moveHandler: (e: TouchEvent) => {\n            if (e.cancelable) {\n              e.preventDefault()\n              e.stopPropagation()\n            }\n            onMove(e.touches[0]!.clientX)\n            return false\n          },\n          upHandler: (e: TouchEvent) => {\n            contextDocument?.removeEventListener(\n              'touchmove',\n              touchEvents.moveHandler\n            )\n            contextDocument?.removeEventListener(\n              'touchend',\n              touchEvents.upHandler\n            )\n            if (e.cancelable) {\n              e.preventDefault()\n              e.stopPropagation()\n            }\n            onEnd(e.touches[0]?.clientX)\n          },\n        }\n\n        const passiveIfSupported = passiveEventSupported()\n          ? { passive: false }\n          : false\n\n        if (isTouchStartEvent(e)) {\n          contextDocument?.addEventListener(\n            'touchmove',\n            touchEvents.moveHandler,\n            passiveIfSupported\n          )\n          contextDocument?.addEventListener(\n            'touchend',\n            touchEvents.upHandler,\n            passiveIfSupported\n          )\n        } else {\n          contextDocument?.addEventListener(\n            'mousemove',\n            mouseEvents.moveHandler,\n            passiveIfSupported\n          )\n          contextDocument?.addEventListener(\n            'mouseup',\n            mouseEvents.upHandler,\n            passiveIfSupported\n          )\n        }\n\n        table.setColumnSizingInfo(old => ({\n          ...old,\n          startOffset: clientX,\n          startSize,\n          deltaOffset: 0,\n          deltaPercentage: 0,\n          columnSizingStart,\n          isResizingColumn: column.id,\n        }))\n      }\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setColumnSizing = updater =>\n      table.options.onColumnSizingChange?.(updater)\n    table.setColumnSizingInfo = updater =>\n      table.options.onColumnSizingInfoChange?.(updater)\n    table.resetColumnSizing = defaultState => {\n      table.setColumnSizing(\n        defaultState ? {} : table.initialState.columnSizing ?? {}\n      )\n    }\n    table.resetHeaderSizeInfo = defaultState => {\n      table.setColumnSizingInfo(\n        defaultState\n          ? getDefaultColumnSizingInfoState()\n          : table.initialState.columnSizingInfo ??\n              getDefaultColumnSizingInfoState()\n      )\n    }\n    table.getTotalSize = () =>\n      table.getHeaderGroups()[0]?.headers.reduce((sum, header) => {\n        return sum + header.getSize()\n      }, 0) ?? 0\n    table.getLeftTotalSize = () =>\n      table.getLeftHeaderGroups()[0]?.headers.reduce((sum, header) => {\n        return sum + header.getSize()\n      }, 0) ?? 0\n    table.getCenterTotalSize = () =>\n      table.getCenterHeaderGroups()[0]?.headers.reduce((sum, header) => {\n        return sum + header.getSize()\n      }, 0) ?? 0\n    table.getRightTotalSize = () =>\n      table.getRightHeaderGroups()[0]?.headers.reduce((sum, header) => {\n        return sum + header.getSize()\n      }, 0) ?? 0\n  },\n}\n\nlet passiveSupported: boolean | null = null\nexport function passiveEventSupported() {\n  if (typeof passiveSupported === 'boolean') return passiveSupported\n\n  let supported = false\n  try {\n    const options = {\n      get passive() {\n        supported = true\n        return false\n      },\n    }\n\n    const noop = () => {}\n\n    window.addEventListener('test', noop, options)\n    window.removeEventListener('test', noop)\n  } catch (err) {\n    supported = false\n  }\n  passiveSupported = supported\n  return passiveSupported\n}\n\nfunction isTouchStartEvent(e: unknown): e is TouchEvent {\n  return (e as TouchEvent).type === 'touchstart'\n}\n"], "names": ["defaultColumnSizing", "size", "minSize", "maxSize", "Number", "MAX_SAFE_INTEGER", "getDefaultColumnSizingInfoState", "startOffset", "startSize", "deltaOffset", "deltaPercentage", "isResizingColumn", "columnSizingStart", "ColumnSizing", "getDefaultColumnDef", "getInitialState", "state", "columnSizing", "columnSizingInfo", "getDefaultOptions", "table", "columnResizeMode", "columnResizeDirection", "onColumnSizingChange", "makeStateUpdater", "onColumnSizingInfoChange", "createColumn", "column", "getSize", "_column$columnDef$min", "_ref", "_column$columnDef$max", "columnSize", "getState", "id", "Math", "min", "max", "columnDef", "getStart", "memo", "position", "_getVisibleLeafColumns", "columns", "slice", "getIndex", "reduce", "sum", "getMemoOptions", "options", "getAfter", "resetSize", "setColumnSizing", "_ref2", "_", "rest", "getCanResize", "_column$columnDef$ena", "_table$options$enable", "enableResizing", "enableColumnResizing", "getIsResizing", "createHeader", "header", "recurse", "subHeaders", "length", "for<PERSON>ach", "_header$column$getSiz", "index", "prevSiblingHeader", "headerGroup", "headers", "getResizeHandler", "_contextDocument", "getColumn", "canResize", "e", "persist", "isTouchStartEvent", "touches", "getLeafHeaders", "map", "d", "clientX", "round", "newColumnSizing", "updateOffset", "eventType", "clientXPos", "setColumnSizingInfo", "old", "_old$startOffset", "_old$startSize", "deltaDirection", "_ref3", "columnId", "headerSize", "onMove", "onEnd", "contextDocument", "safelyAccessDocument", "mouseEvents", "<PERSON><PERSON><PERSON><PERSON>", "up<PERSON><PERSON><PERSON>", "removeEventListener", "touchEvents", "cancelable", "preventDefault", "stopPropagation", "_e$touches$", "passiveIfSupported", "passiveEventSupported", "passive", "addEventListener", "createTable", "updater", "resetColumnSizing", "defaultState", "_table$initialState$c", "initialState", "resetHeaderSizeInfo", "_table$initialState$c2", "getTotalSize", "_table$getHeaderGroup", "_table$getHeaderGroup2", "getHeaderGroups", "getLeftTotalSize", "_table$getLeftHeaderG", "_table$getLeftHeaderG2", "getLeftHeaderGroups", "getCenterTotalSize", "_table$getCenterHeade", "_table$getCenterHeade2", "getCenterHeaderGroups", "getRightTotalSize", "_table$getRightHeader", "_table$getRightHeader2", "getRightHeaderGroups", "passiveSupported", "supported", "noop", "window", "err", "type"], "mappings": ";;;;;;;;;;;;;;;;AAcA;;AA2MA;;AAEO,MAAMA,mBAAmB,GAAG;AACjCC,EAAAA,IAAI,EAAE,GAAG;AACTC,EAAAA,OAAO,EAAE,EAAE;EACXC,OAAO,EAAEC,MAAM,CAACC,gBAAAA;AAClB,EAAC;AAED,MAAMC,+BAA+B,GAAGA,OAA8B;AACpEC,EAAAA,WAAW,EAAE,IAAI;AACjBC,EAAAA,SAAS,EAAE,IAAI;AACfC,EAAAA,WAAW,EAAE,IAAI;AACjBC,EAAAA,eAAe,EAAE,IAAI;AACrBC,EAAAA,gBAAgB,EAAE,KAAK;AACvBC,EAAAA,iBAAiB,EAAE,EAAA;AACrB,CAAC,CAAC,CAAA;AAEK,MAAMC,YAA0B,GAAG;EACxCC,mBAAmB,EAAEA,MAA6B;AAChD,IAAA,OAAOd,mBAAmB,CAAA;GAC3B;EACDe,eAAe,EAAGC,KAAK,IAA6B;IAClD,OAAO;MACLC,YAAY,EAAE,EAAE;MAChBC,gBAAgB,EAAEZ,+BAA+B,EAAE;MACnD,GAAGU,KAAAA;KACJ,CAAA;GACF;EAEDG,iBAAiB,EACfC,KAAmB,IACY;IAC/B,OAAO;AACLC,MAAAA,gBAAgB,EAAE,OAAO;AACzBC,MAAAA,qBAAqB,EAAE,KAAK;AAC5BC,MAAAA,oBAAoB,EAAEC,sBAAgB,CAAC,cAAc,EAAEJ,KAAK,CAAC;AAC7DK,MAAAA,wBAAwB,EAAED,sBAAgB,CAAC,kBAAkB,EAAEJ,KAAK,CAAA;KACrE,CAAA;GACF;AAEDM,EAAAA,YAAY,EAAEA,CACZC,MAA6B,EAC7BP,KAAmB,KACV;IACTO,MAAM,CAACC,OAAO,GAAG,MAAM;AAAA,MAAA,IAAAC,qBAAA,EAAAC,IAAA,EAAAC,qBAAA,CAAA;AACrB,MAAA,MAAMC,UAAU,GAAGZ,KAAK,CAACa,QAAQ,EAAE,CAAChB,YAAY,CAACU,MAAM,CAACO,EAAE,CAAC,CAAA;MAE3D,OAAOC,IAAI,CAACC,GAAG,CACbD,IAAI,CAACE,GAAG,CAAA,CAAAR,qBAAA,GACNF,MAAM,CAACW,SAAS,CAACpC,OAAO,KAAA2B,IAAAA,GAAAA,qBAAA,GAAI7B,mBAAmB,CAACE,OAAO,EAAA4B,CAAAA,IAAA,GACvDE,UAAU,IAAVA,IAAAA,GAAAA,UAAU,GAAIL,MAAM,CAACW,SAAS,CAACrC,IAAI,KAAA,IAAA,GAAA6B,IAAA,GAAI9B,mBAAmB,CAACC,IAC7D,CAAC,EAAA,CAAA8B,qBAAA,GACDJ,MAAM,CAACW,SAAS,CAACnC,OAAO,KAAA4B,IAAAA,GAAAA,qBAAA,GAAI/B,mBAAmB,CAACG,OAClD,CAAC,CAAA;KACF,CAAA;AAEDwB,IAAAA,MAAM,CAACY,QAAQ,GAAGC,UAAI,CACpBC,QAAQ,IAAI,CACVA,QAAQ,EACRC,uCAAsB,CAACtB,KAAK,EAAEqB,QAAQ,CAAC,EACvCrB,KAAK,CAACa,QAAQ,EAAE,CAAChB,YAAY,CAC9B,EACD,CAACwB,QAAQ,EAAEE,OAAO,KAChBA,OAAO,CACJC,KAAK,CAAC,CAAC,EAAEjB,MAAM,CAACkB,QAAQ,CAACJ,QAAQ,CAAC,CAAC,CACnCK,MAAM,CAAC,CAACC,GAAG,EAAEpB,MAAM,KAAKoB,GAAG,GAAGpB,MAAM,CAACC,OAAO,EAAE,EAAE,CAAC,CAAC,EACvDoB,oBAAc,CAAC5B,KAAK,CAAC6B,OAAO,EAAE,cAAc,EAAE,UAAU,CAC1D,CAAC,CAAA;AAEDtB,IAAAA,MAAM,CAACuB,QAAQ,GAAGV,UAAI,CACpBC,QAAQ,IAAI,CACVA,QAAQ,EACRC,uCAAsB,CAACtB,KAAK,EAAEqB,QAAQ,CAAC,EACvCrB,KAAK,CAACa,QAAQ,EAAE,CAAChB,YAAY,CAC9B,EACD,CAACwB,QAAQ,EAAEE,OAAO,KAChBA,OAAO,CACJC,KAAK,CAACjB,MAAM,CAACkB,QAAQ,CAACJ,QAAQ,CAAC,GAAG,CAAC,CAAC,CACpCK,MAAM,CAAC,CAACC,GAAG,EAAEpB,MAAM,KAAKoB,GAAG,GAAGpB,MAAM,CAACC,OAAO,EAAE,EAAE,CAAC,CAAC,EACvDoB,oBAAc,CAAC5B,KAAK,CAAC6B,OAAO,EAAE,cAAc,EAAE,UAAU,CAC1D,CAAC,CAAA;IAEDtB,MAAM,CAACwB,SAAS,GAAG,MAAM;AACvB/B,MAAAA,KAAK,CAACgC,eAAe,CAACC,KAAA,IAAiC;QAAA,IAAhC;AAAE,UAAA,CAAC1B,MAAM,CAACO,EAAE,GAAGoB,CAAC;UAAE,GAAGC,IAAAA;AAAK,SAAC,GAAAF,KAAA,CAAA;AAChD,QAAA,OAAOE,IAAI,CAAA;AACb,OAAC,CAAC,CAAA;KACH,CAAA;IACD5B,MAAM,CAAC6B,YAAY,GAAG,MAAM;MAAA,IAAAC,qBAAA,EAAAC,qBAAA,CAAA;MAC1B,OACE,CAAA,CAAAD,qBAAA,GAAC9B,MAAM,CAACW,SAAS,CAACqB,cAAc,KAAAF,IAAAA,GAAAA,qBAAA,GAAI,IAAI,OAAAC,qBAAA,GACvCtC,KAAK,CAAC6B,OAAO,CAACW,oBAAoB,KAAAF,IAAAA,GAAAA,qBAAA,GAAI,IAAI,CAAC,CAAA;KAE/C,CAAA;IACD/B,MAAM,CAACkC,aAAa,GAAG,MAAM;AAC3B,MAAA,OAAOzC,KAAK,CAACa,QAAQ,EAAE,CAACf,gBAAgB,CAACP,gBAAgB,KAAKgB,MAAM,CAACO,EAAE,CAAA;KACxE,CAAA;GACF;AAED4B,EAAAA,YAAY,EAAEA,CACZC,MAA6B,EAC7B3C,KAAmB,KACV;IACT2C,MAAM,CAACnC,OAAO,GAAG,MAAM;MACrB,IAAImB,GAAG,GAAG,CAAC,CAAA;MAEX,MAAMiB,OAAO,GAAID,MAA6B,IAAK;AACjD,QAAA,IAAIA,MAAM,CAACE,UAAU,CAACC,MAAM,EAAE;AAC5BH,UAAAA,MAAM,CAACE,UAAU,CAACE,OAAO,CAACH,OAAO,CAAC,CAAA;AACpC,SAAC,MAAM;AAAA,UAAA,IAAAI,qBAAA,CAAA;AACLrB,UAAAA,GAAG,IAAAqB,CAAAA,qBAAA,GAAIL,MAAM,CAACpC,MAAM,CAACC,OAAO,EAAE,KAAAwC,IAAAA,GAAAA,qBAAA,GAAI,CAAC,CAAA;AACrC,SAAA;OACD,CAAA;MAEDJ,OAAO,CAACD,MAAM,CAAC,CAAA;AAEf,MAAA,OAAOhB,GAAG,CAAA;KACX,CAAA;IACDgB,MAAM,CAACxB,QAAQ,GAAG,MAAM;AACtB,MAAA,IAAIwB,MAAM,CAACM,KAAK,GAAG,CAAC,EAAE;AACpB,QAAA,MAAMC,iBAAiB,GAAGP,MAAM,CAACQ,WAAW,CAACC,OAAO,CAACT,MAAM,CAACM,KAAK,GAAG,CAAC,CAAE,CAAA;QACvE,OAAOC,iBAAiB,CAAC/B,QAAQ,EAAE,GAAG+B,iBAAiB,CAAC1C,OAAO,EAAE,CAAA;AACnE,OAAA;AAEA,MAAA,OAAO,CAAC,CAAA;KACT,CAAA;AACDmC,IAAAA,MAAM,CAACU,gBAAgB,GAAGC,gBAAgB,IAAI;MAC5C,MAAM/C,MAAM,GAAGP,KAAK,CAACuD,SAAS,CAACZ,MAAM,CAACpC,MAAM,CAACO,EAAE,CAAC,CAAA;MAChD,MAAM0C,SAAS,GAAGjD,MAAM,IAAA,IAAA,GAAA,KAAA,CAAA,GAANA,MAAM,CAAE6B,YAAY,EAAE,CAAA;AAExC,MAAA,OAAQqB,CAAU,IAAK;AACrB,QAAA,IAAI,CAAClD,MAAM,IAAI,CAACiD,SAAS,EAAE;AACzB,UAAA,OAAA;AACF,SAAA;AAEEC,QAAAA,CAAC,CAASC,OAAO,IAAA,IAAA,IAAjBD,CAAC,CAASC,OAAO,EAAI,CAAA;AAEvB,QAAA,IAAIC,iBAAiB,CAACF,CAAC,CAAC,EAAE;AACxB;UACA,IAAIA,CAAC,CAACG,OAAO,IAAIH,CAAC,CAACG,OAAO,CAACd,MAAM,GAAG,CAAC,EAAE;AACrC,YAAA,OAAA;AACF,WAAA;AACF,SAAA;AAEA,QAAA,MAAM1D,SAAS,GAAGuD,MAAM,CAACnC,OAAO,EAAE,CAAA;AAElC,QAAA,MAAMhB,iBAAqC,GAAGmD,MAAM,GAChDA,MAAM,CAACkB,cAAc,EAAE,CAACC,GAAG,CAACC,CAAC,IAAI,CAACA,CAAC,CAACxD,MAAM,CAACO,EAAE,EAAEiD,CAAC,CAACxD,MAAM,CAACC,OAAO,EAAE,CAAC,CAAC,GACnE,CAAC,CAACD,MAAM,CAACO,EAAE,EAAEP,MAAM,CAACC,OAAO,EAAE,CAAC,CAAC,CAAA;QAEnC,MAAMwD,OAAO,GAAGL,iBAAiB,CAACF,CAAC,CAAC,GAChC1C,IAAI,CAACkD,KAAK,CAACR,CAAC,CAACG,OAAO,CAAC,CAAC,CAAC,CAAEI,OAAO,CAAC,GAChCP,CAAC,CAAgBO,OAAO,CAAA;QAE7B,MAAME,eAAkC,GAAG,EAAE,CAAA;AAE7C,QAAA,MAAMC,YAAY,GAAGA,CACnBC,SAAyB,EACzBC,UAAmB,KAChB;AACH,UAAA,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;AAClC,YAAA,OAAA;AACF,WAAA;AAEArE,UAAAA,KAAK,CAACsE,mBAAmB,CAACC,GAAG,IAAI;YAAA,IAAAC,gBAAA,EAAAC,cAAA,CAAA;AAC/B,YAAA,MAAMC,cAAc,GAClB1E,KAAK,CAAC6B,OAAO,CAAC3B,qBAAqB,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;AACxD,YAAA,MAAMb,WAAW,GACf,CAACgF,UAAU,IAAAG,CAAAA,gBAAA,GAAID,GAAG,IAAA,IAAA,GAAA,KAAA,CAAA,GAAHA,GAAG,CAAEpF,WAAW,KAAAqF,IAAAA,GAAAA,gBAAA,GAAI,CAAC,CAAC,IAAIE,cAAc,CAAA;YACzD,MAAMpF,eAAe,GAAGyB,IAAI,CAACE,GAAG,CAC9B5B,WAAW,IAAAoF,CAAAA,cAAA,GAAIF,GAAG,oBAAHA,GAAG,CAAEnF,SAAS,KAAA,IAAA,GAAAqF,cAAA,GAAI,CAAC,CAAC,EACnC,CAAC,QACH,CAAC,CAAA;AAEDF,YAAAA,GAAG,CAAC/E,iBAAiB,CAACuD,OAAO,CAAC4B,KAAA,IAA4B;AAAA,cAAA,IAA3B,CAACC,QAAQ,EAAEC,UAAU,CAAC,GAAAF,KAAA,CAAA;cACnDT,eAAe,CAACU,QAAQ,CAAC,GACvB7D,IAAI,CAACkD,KAAK,CACRlD,IAAI,CAACE,GAAG,CAAC4D,UAAU,GAAGA,UAAU,GAAGvF,eAAe,EAAE,CAAC,CAAC,GAAG,GAC3D,CAAC,GAAG,GAAG,CAAA;AACX,aAAC,CAAC,CAAA;YAEF,OAAO;AACL,cAAA,GAAGiF,GAAG;cACNlF,WAAW;AACXC,cAAAA,eAAAA;aACD,CAAA;AACH,WAAC,CAAC,CAAA;UAEF,IACEU,KAAK,CAAC6B,OAAO,CAAC5B,gBAAgB,KAAK,UAAU,IAC7CmE,SAAS,KAAK,KAAK,EACnB;AACApE,YAAAA,KAAK,CAACgC,eAAe,CAACuC,GAAG,KAAK;AAC5B,cAAA,GAAGA,GAAG;cACN,GAAGL,eAAAA;AACL,aAAC,CAAC,CAAC,CAAA;AACL,WAAA;SACD,CAAA;QAED,MAAMY,MAAM,GAAIT,UAAmB,IAAKF,YAAY,CAAC,MAAM,EAAEE,UAAU,CAAC,CAAA;QAExE,MAAMU,KAAK,GAAIV,UAAmB,IAAK;AACrCF,UAAAA,YAAY,CAAC,KAAK,EAAEE,UAAU,CAAC,CAAA;AAE/BrE,UAAAA,KAAK,CAACsE,mBAAmB,CAACC,GAAG,KAAK;AAChC,YAAA,GAAGA,GAAG;AACNhF,YAAAA,gBAAgB,EAAE,KAAK;AACvBJ,YAAAA,WAAW,EAAE,IAAI;AACjBC,YAAAA,SAAS,EAAE,IAAI;AACfC,YAAAA,WAAW,EAAE,IAAI;AACjBC,YAAAA,eAAe,EAAE,IAAI;AACrBE,YAAAA,iBAAiB,EAAE,EAAA;AACrB,WAAC,CAAC,CAAC,CAAA;SACJ,CAAA;AAED,QAAA,MAAMwF,eAAe,GAAGC,6BAAoB,CAAC3B,gBAAgB,CAAC,CAAA;AAE9D,QAAA,MAAM4B,WAAW,GAAG;UAClBC,WAAW,EAAG1B,CAAa,IAAKqB,MAAM,CAACrB,CAAC,CAACO,OAAO,CAAC;UACjDoB,SAAS,EAAG3B,CAAa,IAAK;YAC5BuB,eAAe,IAAA,IAAA,IAAfA,eAAe,CAAEK,mBAAmB,CAClC,WAAW,EACXH,WAAW,CAACC,WACd,CAAC,CAAA;YACDH,eAAe,IAAA,IAAA,IAAfA,eAAe,CAAEK,mBAAmB,CAClC,SAAS,EACTH,WAAW,CAACE,SACd,CAAC,CAAA;AACDL,YAAAA,KAAK,CAACtB,CAAC,CAACO,OAAO,CAAC,CAAA;AAClB,WAAA;SACD,CAAA;AAED,QAAA,MAAMsB,WAAW,GAAG;UAClBH,WAAW,EAAG1B,CAAa,IAAK;YAC9B,IAAIA,CAAC,CAAC8B,UAAU,EAAE;cAChB9B,CAAC,CAAC+B,cAAc,EAAE,CAAA;cAClB/B,CAAC,CAACgC,eAAe,EAAE,CAAA;AACrB,aAAA;YACAX,MAAM,CAACrB,CAAC,CAACG,OAAO,CAAC,CAAC,CAAC,CAAEI,OAAO,CAAC,CAAA;AAC7B,YAAA,OAAO,KAAK,CAAA;WACb;UACDoB,SAAS,EAAG3B,CAAa,IAAK;AAAA,YAAA,IAAAiC,WAAA,CAAA;YAC5BV,eAAe,IAAA,IAAA,IAAfA,eAAe,CAAEK,mBAAmB,CAClC,WAAW,EACXC,WAAW,CAACH,WACd,CAAC,CAAA;YACDH,eAAe,IAAA,IAAA,IAAfA,eAAe,CAAEK,mBAAmB,CAClC,UAAU,EACVC,WAAW,CAACF,SACd,CAAC,CAAA;YACD,IAAI3B,CAAC,CAAC8B,UAAU,EAAE;cAChB9B,CAAC,CAAC+B,cAAc,EAAE,CAAA;cAClB/B,CAAC,CAACgC,eAAe,EAAE,CAAA;AACrB,aAAA;AACAV,YAAAA,KAAK,CAAAW,CAAAA,WAAA,GAACjC,CAAC,CAACG,OAAO,CAAC,CAAC,CAAC,KAAA,IAAA,GAAA,KAAA,CAAA,GAAZ8B,WAAA,CAAc1B,OAAO,CAAC,CAAA;AAC9B,WAAA;SACD,CAAA;AAED,QAAA,MAAM2B,kBAAkB,GAAGC,qBAAqB,EAAE,GAC9C;AAAEC,UAAAA,OAAO,EAAE,KAAA;AAAM,SAAC,GAClB,KAAK,CAAA;AAET,QAAA,IAAIlC,iBAAiB,CAACF,CAAC,CAAC,EAAE;AACxBuB,UAAAA,eAAe,IAAfA,IAAAA,IAAAA,eAAe,CAAEc,gBAAgB,CAC/B,WAAW,EACXR,WAAW,CAACH,WAAW,EACvBQ,kBACF,CAAC,CAAA;AACDX,UAAAA,eAAe,IAAfA,IAAAA,IAAAA,eAAe,CAAEc,gBAAgB,CAC/B,UAAU,EACVR,WAAW,CAACF,SAAS,EACrBO,kBACF,CAAC,CAAA;AACH,SAAC,MAAM;AACLX,UAAAA,eAAe,IAAfA,IAAAA,IAAAA,eAAe,CAAEc,gBAAgB,CAC/B,WAAW,EACXZ,WAAW,CAACC,WAAW,EACvBQ,kBACF,CAAC,CAAA;AACDX,UAAAA,eAAe,IAAfA,IAAAA,IAAAA,eAAe,CAAEc,gBAAgB,CAC/B,SAAS,EACTZ,WAAW,CAACE,SAAS,EACrBO,kBACF,CAAC,CAAA;AACH,SAAA;AAEA3F,QAAAA,KAAK,CAACsE,mBAAmB,CAACC,GAAG,KAAK;AAChC,UAAA,GAAGA,GAAG;AACNpF,UAAAA,WAAW,EAAE6E,OAAO;UACpB5E,SAAS;AACTC,UAAAA,WAAW,EAAE,CAAC;AACdC,UAAAA,eAAe,EAAE,CAAC;UAClBE,iBAAiB;UACjBD,gBAAgB,EAAEgB,MAAM,CAACO,EAAAA;AAC3B,SAAC,CAAC,CAAC,CAAA;OACJ,CAAA;KACF,CAAA;GACF;EAEDiF,WAAW,EAA0B/F,KAAmB,IAAW;AACjEA,IAAAA,KAAK,CAACgC,eAAe,GAAGgE,OAAO,IAC7BhG,KAAK,CAAC6B,OAAO,CAAC1B,oBAAoB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAlCH,KAAK,CAAC6B,OAAO,CAAC1B,oBAAoB,CAAG6F,OAAO,CAAC,CAAA;AAC/ChG,IAAAA,KAAK,CAACsE,mBAAmB,GAAG0B,OAAO,IACjChG,KAAK,CAAC6B,OAAO,CAACxB,wBAAwB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAtCL,KAAK,CAAC6B,OAAO,CAACxB,wBAAwB,CAAG2F,OAAO,CAAC,CAAA;AACnDhG,IAAAA,KAAK,CAACiG,iBAAiB,GAAGC,YAAY,IAAI;AAAA,MAAA,IAAAC,qBAAA,CAAA;MACxCnG,KAAK,CAACgC,eAAe,CACnBkE,YAAY,GAAG,EAAE,IAAAC,qBAAA,GAAGnG,KAAK,CAACoG,YAAY,CAACvG,YAAY,KAAA,IAAA,GAAAsG,qBAAA,GAAI,EACzD,CAAC,CAAA;KACF,CAAA;AACDnG,IAAAA,KAAK,CAACqG,mBAAmB,GAAGH,YAAY,IAAI;AAAA,MAAA,IAAAI,sBAAA,CAAA;MAC1CtG,KAAK,CAACsE,mBAAmB,CACvB4B,YAAY,GACRhH,+BAA+B,EAAE,GAAA,CAAAoH,sBAAA,GACjCtG,KAAK,CAACoG,YAAY,CAACtG,gBAAgB,KAAA,IAAA,GAAAwG,sBAAA,GACjCpH,+BAA+B,EACvC,CAAC,CAAA;KACF,CAAA;IACDc,KAAK,CAACuG,YAAY,GAAG,MAAA;MAAA,IAAAC,qBAAA,EAAAC,sBAAA,CAAA;MAAA,OAAAD,CAAAA,qBAAA,IAAAC,sBAAA,GACnBzG,KAAK,CAAC0G,eAAe,EAAE,CAAC,CAAC,CAAC,KAA1BD,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,sBAAA,CAA4BrD,OAAO,CAAC1B,MAAM,CAAC,CAACC,GAAG,EAAEgB,MAAM,KAAK;AAC1D,QAAA,OAAOhB,GAAG,GAAGgB,MAAM,CAACnC,OAAO,EAAE,CAAA;AAC/B,OAAC,EAAE,CAAC,CAAC,KAAAgG,IAAAA,GAAAA,qBAAA,GAAI,CAAC,CAAA;AAAA,KAAA,CAAA;IACZxG,KAAK,CAAC2G,gBAAgB,GAAG,MAAA;MAAA,IAAAC,qBAAA,EAAAC,sBAAA,CAAA;MAAA,OAAAD,CAAAA,qBAAA,IAAAC,sBAAA,GACvB7G,KAAK,CAAC8G,mBAAmB,EAAE,CAAC,CAAC,CAAC,KAA9BD,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,sBAAA,CAAgCzD,OAAO,CAAC1B,MAAM,CAAC,CAACC,GAAG,EAAEgB,MAAM,KAAK;AAC9D,QAAA,OAAOhB,GAAG,GAAGgB,MAAM,CAACnC,OAAO,EAAE,CAAA;AAC/B,OAAC,EAAE,CAAC,CAAC,KAAAoG,IAAAA,GAAAA,qBAAA,GAAI,CAAC,CAAA;AAAA,KAAA,CAAA;IACZ5G,KAAK,CAAC+G,kBAAkB,GAAG,MAAA;MAAA,IAAAC,qBAAA,EAAAC,sBAAA,CAAA;MAAA,OAAAD,CAAAA,qBAAA,IAAAC,sBAAA,GACzBjH,KAAK,CAACkH,qBAAqB,EAAE,CAAC,CAAC,CAAC,KAAhCD,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,sBAAA,CAAkC7D,OAAO,CAAC1B,MAAM,CAAC,CAACC,GAAG,EAAEgB,MAAM,KAAK;AAChE,QAAA,OAAOhB,GAAG,GAAGgB,MAAM,CAACnC,OAAO,EAAE,CAAA;AAC/B,OAAC,EAAE,CAAC,CAAC,KAAAwG,IAAAA,GAAAA,qBAAA,GAAI,CAAC,CAAA;AAAA,KAAA,CAAA;IACZhH,KAAK,CAACmH,iBAAiB,GAAG,MAAA;MAAA,IAAAC,qBAAA,EAAAC,sBAAA,CAAA;MAAA,OAAAD,CAAAA,qBAAA,IAAAC,sBAAA,GACxBrH,KAAK,CAACsH,oBAAoB,EAAE,CAAC,CAAC,CAAC,KAA/BD,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,sBAAA,CAAiCjE,OAAO,CAAC1B,MAAM,CAAC,CAACC,GAAG,EAAEgB,MAAM,KAAK;AAC/D,QAAA,OAAOhB,GAAG,GAAGgB,MAAM,CAACnC,OAAO,EAAE,CAAA;AAC/B,OAAC,EAAE,CAAC,CAAC,KAAA4G,IAAAA,GAAAA,qBAAA,GAAI,CAAC,CAAA;AAAA,KAAA,CAAA;AACd,GAAA;AACF,EAAC;AAED,IAAIG,gBAAgC,GAAG,IAAI,CAAA;AACpC,SAAS3B,qBAAqBA,GAAG;AACtC,EAAA,IAAI,OAAO2B,gBAAgB,KAAK,SAAS,EAAE,OAAOA,gBAAgB,CAAA;EAElE,IAAIC,SAAS,GAAG,KAAK,CAAA;EACrB,IAAI;AACF,IAAA,MAAM3F,OAAO,GAAG;MACd,IAAIgE,OAAOA,GAAG;AACZ2B,QAAAA,SAAS,GAAG,IAAI,CAAA;AAChB,QAAA,OAAO,KAAK,CAAA;AACd,OAAA;KACD,CAAA;AAED,IAAA,MAAMC,IAAI,GAAGA,MAAM,EAAE,CAAA;IAErBC,MAAM,CAAC5B,gBAAgB,CAAC,MAAM,EAAE2B,IAAI,EAAE5F,OAAO,CAAC,CAAA;AAC9C6F,IAAAA,MAAM,CAACrC,mBAAmB,CAAC,MAAM,EAAEoC,IAAI,CAAC,CAAA;GACzC,CAAC,OAAOE,GAAG,EAAE;AACZH,IAAAA,SAAS,GAAG,KAAK,CAAA;AACnB,GAAA;AACAD,EAAAA,gBAAgB,GAAGC,SAAS,CAAA;AAC5B,EAAA,OAAOD,gBAAgB,CAAA;AACzB,CAAA;AAEA,SAAS5D,iBAAiBA,CAACF,CAAU,EAAmB;AACtD,EAAA,OAAQA,CAAC,CAAgBmE,IAAI,KAAK,YAAY,CAAA;AAChD;;;;;;"}