{"version": 3, "file": "aggregationFns.js", "sources": ["../../src/aggregationFns.ts"], "sourcesContent": ["import { AggregationFn } from './features/ColumnGrouping'\nimport { isNumberArray } from './utils'\n\nconst sum: AggregationFn<any> = (columnId, _leafRows, childRows) => {\n  // It's faster to just add the aggregations together instead of\n  // process leaf nodes individually\n  return childRows.reduce((sum, next) => {\n    const nextValue = next.getValue(columnId)\n    return sum + (typeof nextValue === 'number' ? nextValue : 0)\n  }, 0)\n}\n\nconst min: AggregationFn<any> = (columnId, _leafRows, childRows) => {\n  let min: number | undefined\n\n  childRows.forEach(row => {\n    const value = row.getValue<number>(columnId)\n\n    if (\n      value != null &&\n      (min! > value || (min === undefined && value >= value))\n    ) {\n      min = value\n    }\n  })\n\n  return min\n}\n\nconst max: AggregationFn<any> = (columnId, _leafRows, childRows) => {\n  let max: number | undefined\n\n  childRows.forEach(row => {\n    const value = row.getValue<number>(columnId)\n    if (\n      value != null &&\n      (max! < value || (max === undefined && value >= value))\n    ) {\n      max = value\n    }\n  })\n\n  return max\n}\n\nconst extent: AggregationFn<any> = (columnId, _leafRows, childRows) => {\n  let min: number | undefined\n  let max: number | undefined\n\n  childRows.forEach(row => {\n    const value = row.getValue<number>(columnId)\n    if (value != null) {\n      if (min === undefined) {\n        if (value >= value) min = max = value\n      } else {\n        if (min > value) min = value\n        if (max! < value) max = value\n      }\n    }\n  })\n\n  return [min, max]\n}\n\nconst mean: AggregationFn<any> = (columnId, leafRows) => {\n  let count = 0\n  let sum = 0\n\n  leafRows.forEach(row => {\n    let value = row.getValue<number>(columnId)\n    if (value != null && (value = +value) >= value) {\n      ++count, (sum += value)\n    }\n  })\n\n  if (count) return sum / count\n\n  return\n}\n\nconst median: AggregationFn<any> = (columnId, leafRows) => {\n  if (!leafRows.length) {\n    return\n  }\n\n  const values = leafRows.map(row => row.getValue(columnId))\n  if (!isNumberArray(values)) {\n    return\n  }\n  if (values.length === 1) {\n    return values[0]\n  }\n\n  const mid = Math.floor(values.length / 2)\n  const nums = values.sort((a, b) => a - b)\n  return values.length % 2 !== 0 ? nums[mid] : (nums[mid - 1]! + nums[mid]!) / 2\n}\n\nconst unique: AggregationFn<any> = (columnId, leafRows) => {\n  return Array.from(new Set(leafRows.map(d => d.getValue(columnId))).values())\n}\n\nconst uniqueCount: AggregationFn<any> = (columnId, leafRows) => {\n  return new Set(leafRows.map(d => d.getValue(columnId))).size\n}\n\nconst count: AggregationFn<any> = (_columnId, leafRows) => {\n  return leafRows.length\n}\n\nexport const aggregationFns = {\n  sum,\n  min,\n  max,\n  extent,\n  mean,\n  median,\n  unique,\n  uniqueCount,\n  count,\n}\n\nexport type BuiltInAggregationFn = keyof typeof aggregationFns\n"], "names": ["sum", "columnId", "_leafRows", "childRows", "reduce", "next", "nextValue", "getValue", "min", "for<PERSON>ach", "row", "value", "undefined", "max", "extent", "mean", "leafRows", "count", "median", "length", "values", "map", "isNumberArray", "mid", "Math", "floor", "nums", "sort", "a", "b", "unique", "Array", "from", "Set", "d", "uniqueCount", "size", "_columnId", "aggregationFns"], "mappings": ";;;;;;;;;;;;;;AAGA,MAAMA,GAAuB,GAAGA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,SAAS,KAAK;AAClE;AACA;EACA,OAAOA,SAAS,CAACC,MAAM,CAAC,CAACJ,GAAG,EAAEK,IAAI,KAAK;AACrC,IAAA,MAAMC,SAAS,GAAGD,IAAI,CAACE,QAAQ,CAACN,QAAQ,CAAC,CAAA;IACzC,OAAOD,GAAG,IAAI,OAAOM,SAAS,KAAK,QAAQ,GAAGA,SAAS,GAAG,CAAC,CAAC,CAAA;GAC7D,EAAE,CAAC,CAAC,CAAA;AACP,CAAC,CAAA;AAED,MAAME,GAAuB,GAAGA,CAACP,QAAQ,EAAEC,SAAS,EAAEC,SAAS,KAAK;AAClE,EAAA,IAAIK,GAAuB,CAAA;AAE3BL,EAAAA,SAAS,CAACM,OAAO,CAACC,GAAG,IAAI;AACvB,IAAA,MAAMC,KAAK,GAAGD,GAAG,CAACH,QAAQ,CAASN,QAAQ,CAAC,CAAA;AAE5C,IAAA,IACEU,KAAK,IAAI,IAAI,KACZH,GAAG,GAAIG,KAAK,IAAKH,GAAG,KAAKI,SAAS,IAAID,KAAK,IAAIA,KAAM,CAAC,EACvD;AACAH,MAAAA,GAAG,GAAGG,KAAK,CAAA;AACb,KAAA;AACF,GAAC,CAAC,CAAA;AAEF,EAAA,OAAOH,GAAG,CAAA;AACZ,CAAC,CAAA;AAED,MAAMK,GAAuB,GAAGA,CAACZ,QAAQ,EAAEC,SAAS,EAAEC,SAAS,KAAK;AAClE,EAAA,IAAIU,GAAuB,CAAA;AAE3BV,EAAAA,SAAS,CAACM,OAAO,CAACC,GAAG,IAAI;AACvB,IAAA,MAAMC,KAAK,GAAGD,GAAG,CAACH,QAAQ,CAASN,QAAQ,CAAC,CAAA;AAC5C,IAAA,IACEU,KAAK,IAAI,IAAI,KACZE,GAAG,GAAIF,KAAK,IAAKE,GAAG,KAAKD,SAAS,IAAID,KAAK,IAAIA,KAAM,CAAC,EACvD;AACAE,MAAAA,GAAG,GAAGF,KAAK,CAAA;AACb,KAAA;AACF,GAAC,CAAC,CAAA;AAEF,EAAA,OAAOE,GAAG,CAAA;AACZ,CAAC,CAAA;AAED,MAAMC,MAA0B,GAAGA,CAACb,QAAQ,EAAEC,SAAS,EAAEC,SAAS,KAAK;AACrE,EAAA,IAAIK,GAAuB,CAAA;AAC3B,EAAA,IAAIK,GAAuB,CAAA;AAE3BV,EAAAA,SAAS,CAACM,OAAO,CAACC,GAAG,IAAI;AACvB,IAAA,MAAMC,KAAK,GAAGD,GAAG,CAACH,QAAQ,CAASN,QAAQ,CAAC,CAAA;IAC5C,IAAIU,KAAK,IAAI,IAAI,EAAE;MACjB,IAAIH,GAAG,KAAKI,SAAS,EAAE;QACrB,IAAID,KAAK,IAAIA,KAAK,EAAEH,GAAG,GAAGK,GAAG,GAAGF,KAAK,CAAA;AACvC,OAAC,MAAM;AACL,QAAA,IAAIH,GAAG,GAAGG,KAAK,EAAEH,GAAG,GAAGG,KAAK,CAAA;AAC5B,QAAA,IAAIE,GAAG,GAAIF,KAAK,EAAEE,GAAG,GAAGF,KAAK,CAAA;AAC/B,OAAA;AACF,KAAA;AACF,GAAC,CAAC,CAAA;AAEF,EAAA,OAAO,CAACH,GAAG,EAAEK,GAAG,CAAC,CAAA;AACnB,CAAC,CAAA;AAED,MAAME,IAAwB,GAAGA,CAACd,QAAQ,EAAEe,QAAQ,KAAK;EACvD,IAAIC,KAAK,GAAG,CAAC,CAAA;EACb,IAAIjB,GAAG,GAAG,CAAC,CAAA;AAEXgB,EAAAA,QAAQ,CAACP,OAAO,CAACC,GAAG,IAAI;AACtB,IAAA,IAAIC,KAAK,GAAGD,GAAG,CAACH,QAAQ,CAASN,QAAQ,CAAC,CAAA;IAC1C,IAAIU,KAAK,IAAI,IAAI,IAAI,CAACA,KAAK,GAAG,CAACA,KAAK,KAAKA,KAAK,EAAE;AAC9C,MAAA,EAAEM,KAAK,EAAGjB,GAAG,IAAIW,KAAM,CAAA;AACzB,KAAA;AACF,GAAC,CAAC,CAAA;AAEF,EAAA,IAAIM,KAAK,EAAE,OAAOjB,GAAG,GAAGiB,KAAK,CAAA;AAE7B,EAAA,OAAA;AACF,CAAC,CAAA;AAED,MAAMC,MAA0B,GAAGA,CAACjB,QAAQ,EAAEe,QAAQ,KAAK;AACzD,EAAA,IAAI,CAACA,QAAQ,CAACG,MAAM,EAAE;AACpB,IAAA,OAAA;AACF,GAAA;AAEA,EAAA,MAAMC,MAAM,GAAGJ,QAAQ,CAACK,GAAG,CAACX,GAAG,IAAIA,GAAG,CAACH,QAAQ,CAACN,QAAQ,CAAC,CAAC,CAAA;AAC1D,EAAA,IAAI,CAACqB,mBAAa,CAACF,MAAM,CAAC,EAAE;AAC1B,IAAA,OAAA;AACF,GAAA;AACA,EAAA,IAAIA,MAAM,CAACD,MAAM,KAAK,CAAC,EAAE;IACvB,OAAOC,MAAM,CAAC,CAAC,CAAC,CAAA;AAClB,GAAA;EAEA,MAAMG,GAAG,GAAGC,IAAI,CAACC,KAAK,CAACL,MAAM,CAACD,MAAM,GAAG,CAAC,CAAC,CAAA;AACzC,EAAA,MAAMO,IAAI,GAAGN,MAAM,CAACO,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC,CAAA;EACzC,OAAOT,MAAM,CAACD,MAAM,GAAG,CAAC,KAAK,CAAC,GAAGO,IAAI,CAACH,GAAG,CAAC,GAAG,CAACG,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,GAAIG,IAAI,CAACH,GAAG,CAAE,IAAI,CAAC,CAAA;AAChF,CAAC,CAAA;AAED,MAAMO,MAA0B,GAAGA,CAAC7B,QAAQ,EAAEe,QAAQ,KAAK;EACzD,OAAOe,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAACjB,QAAQ,CAACK,GAAG,CAACa,CAAC,IAAIA,CAAC,CAAC3B,QAAQ,CAACN,QAAQ,CAAC,CAAC,CAAC,CAACmB,MAAM,EAAE,CAAC,CAAA;AAC9E,CAAC,CAAA;AAED,MAAMe,WAA+B,GAAGA,CAAClC,QAAQ,EAAEe,QAAQ,KAAK;AAC9D,EAAA,OAAO,IAAIiB,GAAG,CAACjB,QAAQ,CAACK,GAAG,CAACa,CAAC,IAAIA,CAAC,CAAC3B,QAAQ,CAACN,QAAQ,CAAC,CAAC,CAAC,CAACmC,IAAI,CAAA;AAC9D,CAAC,CAAA;AAED,MAAMnB,KAAyB,GAAGA,CAACoB,SAAS,EAAErB,QAAQ,KAAK;EACzD,OAAOA,QAAQ,CAACG,MAAM,CAAA;AACxB,CAAC,CAAA;AAEM,MAAMmB,cAAc,GAAG;EAC5BtC,GAAG;EACHQ,GAAG;EACHK,GAAG;EACHC,MAAM;EACNC,IAAI;EACJG,MAAM;EACNY,MAAM;EACNK,WAAW;AACXlB,EAAAA,KAAAA;AACF;;;;"}