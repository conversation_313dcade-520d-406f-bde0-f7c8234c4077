{"version": 3, "file": "ColumnFaceting.js", "sources": ["../../../src/features/ColumnFaceting.ts"], "sourcesContent": ["import { RowModel } from '..'\nimport { Column, RowData, Table, TableFeature } from '../types'\n\nexport interface FacetedColumn<TData extends RowData> {\n  _getFacetedMinMaxValues?: () => undefined | [number, number]\n  _getFacetedRowModel?: () => RowModel<TData>\n  _getFacetedUniqueValues?: () => Map<any, number>\n  /**\n   * A function that **computes and returns** a min/max tuple derived from `column.getFacetedRowModel`. Useful for displaying faceted result values.\n   * > ⚠️ Requires that you pass a valid `getFacetedMinMaxValues` function to `options.getFacetedMinMaxValues`. A default implementation is provided via the exported `getFacetedMinMaxValues` function.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-faceting#getfacetedminmaxvalues)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-faceting)\n   */\n  getFacetedMinMaxValues: () => undefined | [number, number]\n  /**\n   * Returns the row model with all other column filters applied, excluding its own filter. Useful for displaying faceted result counts.\n   * > ⚠️ Requires that you pass a valid `getFacetedRowModel` function to `options.facetedRowModel`. A default implementation is provided via the exported `getFacetedRowModel` function.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-faceting#getfacetedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-faceting)\n   */\n  getFacetedRowModel: () => RowModel<TData>\n  /**\n   * A function that **computes and returns** a `Map` of unique values and their occurrences derived from `column.getFacetedRowModel`. Useful for displaying faceted result values.\n   * > ⚠️ Requires that you pass a valid `getFacetedUniqueValues` function to `options.getFacetedUniqueValues`. A default implementation is provided via the exported `getFacetedUniqueValues` function.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-faceting#getfaceteduniquevalues)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-faceting)\n   */\n  getFacetedUniqueValues: () => Map<any, number>\n}\n\nexport interface FacetedOptions<TData extends RowData> {\n  getFacetedMinMaxValues?: (\n    table: Table<TData>,\n    columnId: string\n  ) => () => undefined | [number, number]\n  getFacetedRowModel?: (\n    table: Table<TData>,\n    columnId: string\n  ) => () => RowModel<TData>\n  getFacetedUniqueValues?: (\n    table: Table<TData>,\n    columnId: string\n  ) => () => Map<any, number>\n}\n\n//\n\nexport const ColumnFaceting: TableFeature = {\n  createColumn: <TData extends RowData>(\n    column: Column<TData, unknown>,\n    table: Table<TData>\n  ): void => {\n    column._getFacetedRowModel =\n      table.options.getFacetedRowModel &&\n      table.options.getFacetedRowModel(table, column.id)\n    column.getFacetedRowModel = () => {\n      if (!column._getFacetedRowModel) {\n        return table.getPreFilteredRowModel()\n      }\n\n      return column._getFacetedRowModel()\n    }\n    column._getFacetedUniqueValues =\n      table.options.getFacetedUniqueValues &&\n      table.options.getFacetedUniqueValues(table, column.id)\n    column.getFacetedUniqueValues = () => {\n      if (!column._getFacetedUniqueValues) {\n        return new Map()\n      }\n\n      return column._getFacetedUniqueValues()\n    }\n    column._getFacetedMinMaxValues =\n      table.options.getFacetedMinMaxValues &&\n      table.options.getFacetedMinMaxValues(table, column.id)\n    column.getFacetedMinMaxValues = () => {\n      if (!column._getFacetedMinMaxValues) {\n        return undefined\n      }\n\n      return column._getFacetedMinMaxValues()\n    }\n  },\n}\n"], "names": ["ColumnFaceting", "createColumn", "column", "table", "_getFacetedRowModel", "options", "getFacetedRowModel", "id", "getPreFilteredRowModel", "_getFacetedUniqueValues", "getFacetedUniqueValues", "Map", "_getFacetedMinMaxValues", "getFacetedMinMaxValues", "undefined"], "mappings": ";;;;;;;;;;;;AA6CA;;AAEO,MAAMA,cAA4B,GAAG;AAC1CC,EAAAA,YAAY,EAAEA,CACZC,MAA8B,EAC9BC,KAAmB,KACV;IACTD,MAAM,CAACE,mBAAmB,GACxBD,KAAK,CAACE,OAAO,CAACC,kBAAkB,IAChCH,KAAK,CAACE,OAAO,CAACC,kBAAkB,CAACH,KAAK,EAAED,MAAM,CAACK,EAAE,CAAC,CAAA;IACpDL,MAAM,CAACI,kBAAkB,GAAG,MAAM;AAChC,MAAA,IAAI,CAACJ,MAAM,CAACE,mBAAmB,EAAE;AAC/B,QAAA,OAAOD,KAAK,CAACK,sBAAsB,EAAE,CAAA;AACvC,OAAA;AAEA,MAAA,OAAON,MAAM,CAACE,mBAAmB,EAAE,CAAA;KACpC,CAAA;IACDF,MAAM,CAACO,uBAAuB,GAC5BN,KAAK,CAACE,OAAO,CAACK,sBAAsB,IACpCP,KAAK,CAACE,OAAO,CAACK,sBAAsB,CAACP,KAAK,EAAED,MAAM,CAACK,EAAE,CAAC,CAAA;IACxDL,MAAM,CAACQ,sBAAsB,GAAG,MAAM;AACpC,MAAA,IAAI,CAACR,MAAM,CAACO,uBAAuB,EAAE;QACnC,OAAO,IAAIE,GAAG,EAAE,CAAA;AAClB,OAAA;AAEA,MAAA,OAAOT,MAAM,CAACO,uBAAuB,EAAE,CAAA;KACxC,CAAA;IACDP,MAAM,CAACU,uBAAuB,GAC5BT,KAAK,CAACE,OAAO,CAACQ,sBAAsB,IACpCV,KAAK,CAACE,OAAO,CAACQ,sBAAsB,CAACV,KAAK,EAAED,MAAM,CAACK,EAAE,CAAC,CAAA;IACxDL,MAAM,CAACW,sBAAsB,GAAG,MAAM;AACpC,MAAA,IAAI,CAACX,MAAM,CAACU,uBAAuB,EAAE;AACnC,QAAA,OAAOE,SAAS,CAAA;AAClB,OAAA;AAEA,MAAA,OAAOZ,MAAM,CAACU,uBAAuB,EAAE,CAAA;KACxC,CAAA;AACH,GAAA;AACF;;;;"}