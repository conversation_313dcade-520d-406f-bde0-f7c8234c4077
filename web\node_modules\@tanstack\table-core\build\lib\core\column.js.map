{"version": 3, "file": "column.js", "sources": ["../../../src/core/column.ts"], "sourcesContent": ["import {\n  Column,\n  Table,\n  AccessorFn,\n  ColumnDef,\n  RowData,\n  ColumnDefResolved,\n} from '../types'\nimport { getMemoOptions, memo } from '../utils'\n\nexport interface CoreColumn<TData extends RowData, TValue> {\n  /**\n   * The resolved accessor function to use when extracting the value for the column from each row. Will only be defined if the column def has a valid accessor key or function defined.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#accessorfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  accessorFn?: AccessorFn<TData, TValue>\n  /**\n   * The original column def used to create the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#columndef)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  columnDef: ColumnDef<TData, TValue>\n  /**\n   * The child column (if the column is a group column). Will be an empty array if the column is not a group column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#columns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  columns: Column<TData, TValue>[]\n  /**\n   * The depth of the column (if grouped) relative to the root column def array.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#depth)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  depth: number\n  /**\n   * Returns the flattened array of this column and all child/grand-child columns for this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#getflatcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  getFlatColumns: () => Column<TData, TValue>[]\n  /**\n   * Returns an array of all leaf-node columns for this column. If a column has no children, it is considered the only leaf-node column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#getleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  getLeafColumns: () => Column<TData, TValue>[]\n  /**\n   * The resolved unique identifier for the column resolved in this priority:\n      - A manual `id` property from the column def\n      - The accessor key from the column def\n      - The header string from the column def\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#id)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  id: string\n  /**\n   * The parent column for this column. Will be undefined if this is a root column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#parent)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  parent?: Column<TData, TValue>\n}\n\nexport function createColumn<TData extends RowData, TValue>(\n  table: Table<TData>,\n  columnDef: ColumnDef<TData, TValue>,\n  depth: number,\n  parent?: Column<TData, TValue>\n): Column<TData, TValue> {\n  const defaultColumn = table._getDefaultColumnDef()\n\n  const resolvedColumnDef = {\n    ...defaultColumn,\n    ...columnDef,\n  } as ColumnDefResolved<TData>\n\n  const accessorKey = resolvedColumnDef.accessorKey\n\n  let id =\n    resolvedColumnDef.id ??\n    (accessorKey\n      ? typeof String.prototype.replaceAll === 'function'\n        ? accessorKey.replaceAll('.', '_')\n        : accessorKey.replace(/\\./g, '_')\n      : undefined) ??\n    (typeof resolvedColumnDef.header === 'string'\n      ? resolvedColumnDef.header\n      : undefined)\n\n  let accessorFn: AccessorFn<TData> | undefined\n\n  if (resolvedColumnDef.accessorFn) {\n    accessorFn = resolvedColumnDef.accessorFn\n  } else if (accessorKey) {\n    // Support deep accessor keys\n    if (accessorKey.includes('.')) {\n      accessorFn = (originalRow: TData) => {\n        let result = originalRow as Record<string, any>\n\n        for (const key of accessorKey.split('.')) {\n          result = result?.[key]\n          if (process.env.NODE_ENV !== 'production' && result === undefined) {\n            console.warn(\n              `\"${key}\" in deeply nested key \"${accessorKey}\" returned undefined.`\n            )\n          }\n        }\n\n        return result\n      }\n    } else {\n      accessorFn = (originalRow: TData) =>\n        (originalRow as any)[resolvedColumnDef.accessorKey]\n    }\n  }\n\n  if (!id) {\n    if (process.env.NODE_ENV !== 'production') {\n      throw new Error(\n        resolvedColumnDef.accessorFn\n          ? `Columns require an id when using an accessorFn`\n          : `Columns require an id when using a non-string header`\n      )\n    }\n    throw new Error()\n  }\n\n  let column: CoreColumn<TData, any> = {\n    id: `${String(id)}`,\n    accessorFn,\n    parent: parent as any,\n    depth,\n    columnDef: resolvedColumnDef as ColumnDef<TData, any>,\n    columns: [],\n    getFlatColumns: memo(\n      () => [true],\n      () => {\n        return [\n          column as Column<TData, TValue>,\n          ...column.columns?.flatMap(d => d.getFlatColumns()),\n        ]\n      },\n      getMemoOptions(table.options, 'debugColumns', 'column.getFlatColumns')\n    ),\n    getLeafColumns: memo(\n      () => [table._getOrderColumnsFn()],\n      orderColumns => {\n        if (column.columns?.length) {\n          let leafColumns = column.columns.flatMap(column =>\n            column.getLeafColumns()\n          )\n\n          return orderColumns(leafColumns)\n        }\n\n        return [column as Column<TData, TValue>]\n      },\n      getMemoOptions(table.options, 'debugColumns', 'column.getLeafColumns')\n    ),\n  }\n\n  for (const feature of table._features) {\n    feature.createColumn?.(column as Column<TData, TValue>, table)\n  }\n\n  // Yes, we have to convert table to unknown, because we know more than the compiler here.\n  return column as Column<TData, TValue>\n}\n"], "names": ["createColumn", "table", "columnDef", "depth", "parent", "_ref", "_resolvedColumnDef$id", "defaultColumn", "_getDefaultColumnDef", "resolvedColumnDef", "accessorKey", "id", "String", "prototype", "replaceAll", "replace", "undefined", "header", "accessorFn", "includes", "originalRow", "result", "key", "split", "_result", "process", "env", "NODE_ENV", "console", "warn", "Error", "column", "columns", "getFlatColumns", "memo", "_column$columns", "flatMap", "d", "getMemoOptions", "options", "getLeafColumns", "_getOrderColumnsFn", "orderColumns", "_column$columns2", "length", "leafColumns", "feature", "_features"], "mappings": ";;;;;;;;;;;;;;AAgEO,SAASA,YAAYA,CAC1BC,KAAmB,EACnBC,SAAmC,EACnCC,KAAa,EACbC,MAA8B,EACP;EAAA,IAAAC,IAAA,EAAAC,qBAAA,CAAA;AACvB,EAAA,MAAMC,aAAa,GAAGN,KAAK,CAACO,oBAAoB,EAAE,CAAA;AAElD,EAAA,MAAMC,iBAAiB,GAAG;AACxB,IAAA,GAAGF,aAAa;IAChB,GAAGL,SAAAA;GACwB,CAAA;AAE7B,EAAA,MAAMQ,WAAW,GAAGD,iBAAiB,CAACC,WAAW,CAAA;EAEjD,IAAIC,EAAE,GAAAN,CAAAA,IAAA,GAAAC,CAAAA,qBAAA,GACJG,iBAAiB,CAACE,EAAE,KAAAL,IAAAA,GAAAA,qBAAA,GACnBI,WAAW,GACR,OAAOE,MAAM,CAACC,SAAS,CAACC,UAAU,KAAK,UAAU,GAC/CJ,WAAW,CAACI,UAAU,CAAC,GAAG,EAAE,GAAG,CAAC,GAChCJ,WAAW,CAACK,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,GACjCC,SAAS,KAAA,IAAA,GAAAX,IAAA,GACZ,OAAOI,iBAAiB,CAACQ,MAAM,KAAK,QAAQ,GACzCR,iBAAiB,CAACQ,MAAM,GACxBD,SAAU,CAAA;AAEhB,EAAA,IAAIE,UAAyC,CAAA;EAE7C,IAAIT,iBAAiB,CAACS,UAAU,EAAE;IAChCA,UAAU,GAAGT,iBAAiB,CAACS,UAAU,CAAA;GAC1C,MAAM,IAAIR,WAAW,EAAE;AACtB;AACA,IAAA,IAAIA,WAAW,CAACS,QAAQ,CAAC,GAAG,CAAC,EAAE;MAC7BD,UAAU,GAAIE,WAAkB,IAAK;QACnC,IAAIC,MAAM,GAAGD,WAAkC,CAAA;QAE/C,KAAK,MAAME,GAAG,IAAIZ,WAAW,CAACa,KAAK,CAAC,GAAG,CAAC,EAAE;AAAA,UAAA,IAAAC,OAAA,CAAA;UACxCH,MAAM,GAAA,CAAAG,OAAA,GAAGH,MAAM,qBAANG,OAAA,CAASF,GAAG,CAAC,CAAA;UACtB,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAIN,MAAM,KAAKL,SAAS,EAAE;YACjEY,OAAO,CAACC,IAAI,CACV,CAAA,CAAA,EAAIP,GAAG,CAA2BZ,wBAAAA,EAAAA,WAAW,uBAC/C,CAAC,CAAA;AACH,WAAA;AACF,SAAA;AAEA,QAAA,OAAOW,MAAM,CAAA;OACd,CAAA;AACH,KAAC,MAAM;MACLH,UAAU,GAAIE,WAAkB,IAC7BA,WAAW,CAASX,iBAAiB,CAACC,WAAW,CAAC,CAAA;AACvD,KAAA;AACF,GAAA;EAEA,IAAI,CAACC,EAAE,EAAE;AACP,IAAA,IAAIc,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,MAAM,IAAIG,KAAK,CACbrB,iBAAiB,CAACS,UAAU,GACxB,CAAA,8CAAA,CAAgD,GAChD,CAAA,oDAAA,CACN,CAAC,CAAA;AACH,KAAA;IACA,MAAM,IAAIY,KAAK,EAAE,CAAA;AACnB,GAAA;AAEA,EAAA,IAAIC,MAA8B,GAAG;AACnCpB,IAAAA,EAAE,EAAE,CAAGC,EAAAA,MAAM,CAACD,EAAE,CAAC,CAAE,CAAA;IACnBO,UAAU;AACVd,IAAAA,MAAM,EAAEA,MAAa;IACrBD,KAAK;AACLD,IAAAA,SAAS,EAAEO,iBAA0C;AACrDuB,IAAAA,OAAO,EAAE,EAAE;IACXC,cAAc,EAAEC,UAAI,CAClB,MAAM,CAAC,IAAI,CAAC,EACZ,MAAM;AAAA,MAAA,IAAAC,eAAA,CAAA;MACJ,OAAO,CACLJ,MAAM,EACN,IAAA,CAAAI,eAAA,GAAGJ,MAAM,CAACC,OAAO,KAAdG,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,eAAA,CAAgBC,OAAO,CAACC,CAAC,IAAIA,CAAC,CAACJ,cAAc,EAAE,CAAC,EACpD,CAAA;KACF,EACDK,oBAAc,CAACrC,KAAK,CAACsC,OAAO,EAAE,cAAc,EAAE,uBAAuB,CACvE,CAAC;AACDC,IAAAA,cAAc,EAAEN,UAAI,CAClB,MAAM,CAACjC,KAAK,CAACwC,kBAAkB,EAAE,CAAC,EAClCC,YAAY,IAAI;AAAA,MAAA,IAAAC,gBAAA,CAAA;MACd,IAAAA,CAAAA,gBAAA,GAAIZ,MAAM,CAACC,OAAO,KAAdW,IAAAA,IAAAA,gBAAA,CAAgBC,MAAM,EAAE;AAC1B,QAAA,IAAIC,WAAW,GAAGd,MAAM,CAACC,OAAO,CAACI,OAAO,CAACL,MAAM,IAC7CA,MAAM,CAACS,cAAc,EACvB,CAAC,CAAA;QAED,OAAOE,YAAY,CAACG,WAAW,CAAC,CAAA;AAClC,OAAA;MAEA,OAAO,CAACd,MAAM,CAA0B,CAAA;KACzC,EACDO,oBAAc,CAACrC,KAAK,CAACsC,OAAO,EAAE,cAAc,EAAE,uBAAuB,CACvE,CAAA;GACD,CAAA;AAED,EAAA,KAAK,MAAMO,OAAO,IAAI7C,KAAK,CAAC8C,SAAS,EAAE;IACrCD,OAAO,CAAC9C,YAAY,IAAA,IAAA,IAApB8C,OAAO,CAAC9C,YAAY,CAAG+B,MAAM,EAA2B9B,KAAK,CAAC,CAAA;AAChE,GAAA;;AAEA;AACA,EAAA,OAAO8B,MAAM,CAAA;AACf;;;;"}