{"version": 3, "file": "document.js", "sources": ["../../../src/utils/document.ts"], "sourcesContent": ["export function safelyAccessDocument(_document?: Document): Document | null {\n  return _document || (typeof document !== 'undefined' ? document : null)\n}\n\nexport function safelyAccessDocumentEvent(event: Event): Document | null {\n  return !!event &&\n    !!event.target &&\n    typeof event.target === 'object' &&\n    'ownerDocument' in event.target\n    ? (event.target.ownerDocument as Document | null)\n    : null\n}\n"], "names": ["safelyAccessDocument", "_document", "document"], "mappings": ";;;;;;;;;;;;AAAO,SAASA,oBAAoBA,CAACC,SAAoB,EAAmB;EAC1E,OAAOA,SAAS,KAAK,OAAOC,QAAQ,KAAK,WAAW,GAAGA,QAAQ,GAAG,IAAI,CAAC,CAAA;AACzE;;;;"}