{"version": 3, "file": "utils.js", "sources": ["../../src/utils.ts"], "sourcesContent": ["import { TableOptionsResolved, TableState, Updater } from './types'\n\nexport type PartialKeys<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>\nexport type RequiredKeys<T, K extends keyof T> = Omit<T, K> &\n  Required<Pick<T, K>>\nexport type Overwrite<T, U extends { [TKey in keyof T]?: any }> = Omit<\n  T,\n  keyof U\n> &\n  U\n\nexport type UnionToIntersection<T> = (\n  T extends any ? (x: T) => any : never\n) extends (x: infer R) => any\n  ? R\n  : never\n\nexport type IsAny<T, Y, N> = 1 extends 0 & T ? Y : N\nexport type IsKnown<T, Y, N> = unknown extends T ? N : Y\n\ntype ComputeRange<\n  N extends number,\n  Result extends Array<unknown> = [],\n> = Result['length'] extends N\n  ? Result\n  : ComputeRange<N, [...Result, Result['length']]>\ntype Index40 = ComputeRange<40>[number]\n\n// Is this type a tuple?\ntype IsTuple<T> = T extends readonly any[] & { length: infer Length }\n  ? Length extends Index40\n    ? T\n    : never\n  : never\n\n// If this type is a tuple, what indices are allowed?\ntype AllowedIndexes<\n  Tuple extends ReadonlyArray<any>,\n  Keys extends number = never,\n> = Tuple extends readonly []\n  ? Keys\n  : Tuple extends readonly [infer _, ...infer Tail]\n    ? AllowedIndexes<Tail, Keys | Tail['length']>\n    : Keys\n\nexport type DeepKeys<T, TDepth extends any[] = []> = TDepth['length'] extends 5\n  ? never\n  : unknown extends T\n    ? string\n    : T extends readonly any[] & IsTuple<T>\n      ? AllowedIndexes<T> | DeepKeysPrefix<T, AllowedIndexes<T>, TDepth>\n      : T extends any[]\n        ? DeepKeys<T[number], [...TDepth, any]>\n        : T extends Date\n          ? never\n          : T extends object\n            ? (keyof T & string) | DeepKeysPrefix<T, keyof T, TDepth>\n            : never\n\ntype DeepKeysPrefix<\n  T,\n  TPrefix,\n  TDepth extends any[],\n> = TPrefix extends keyof T & (number | string)\n  ? `${TPrefix}.${DeepKeys<T[TPrefix], [...TDepth, any]> & string}`\n  : never\n\nexport type DeepValue<T, TProp> =\n  T extends Record<string | number, any>\n    ? TProp extends `${infer TBranch}.${infer TDeepProp}`\n      ? DeepValue<T[TBranch], TDeepProp>\n      : T[TProp & string]\n    : never\n\nexport type NoInfer<T> = [T][T extends any ? 0 : never]\n\nexport type Getter<TValue> = <TTValue = TValue>() => NoInfer<TTValue>\n\n///\n\nexport function functionalUpdate<T>(updater: Updater<T>, input: T): T {\n  return typeof updater === 'function'\n    ? (updater as (input: T) => T)(input)\n    : updater\n}\n\nexport function noop() {\n  //\n}\n\nexport function makeStateUpdater<K extends keyof TableState>(\n  key: K,\n  instance: unknown\n) {\n  return (updater: Updater<TableState[K]>) => {\n    ;(instance as any).setState(<TTableState>(old: TTableState) => {\n      return {\n        ...old,\n        [key]: functionalUpdate(updater, (old as any)[key]),\n      }\n    })\n  }\n}\n\ntype AnyFunction = (...args: any) => any\n\nexport function isFunction<T extends AnyFunction>(d: any): d is T {\n  return d instanceof Function\n}\n\nexport function isNumberArray(d: any): d is number[] {\n  return Array.isArray(d) && d.every(val => typeof val === 'number')\n}\n\nexport function flattenBy<TNode>(\n  arr: TNode[],\n  getChildren: (item: TNode) => TNode[]\n) {\n  const flat: TNode[] = []\n\n  const recurse = (subArr: TNode[]) => {\n    subArr.forEach(item => {\n      flat.push(item)\n      const children = getChildren(item)\n      if (children?.length) {\n        recurse(children)\n      }\n    })\n  }\n\n  recurse(arr)\n\n  return flat\n}\n\nexport function memo<TDeps extends readonly any[], TDepArgs, TResult>(\n  getDeps: (depArgs?: TDepArgs) => [...TDeps],\n  fn: (...args: NoInfer<[...TDeps]>) => TResult,\n  opts: {\n    key: any\n    debug?: () => any\n    onChange?: (result: TResult) => void\n  }\n): (depArgs?: TDepArgs) => TResult {\n  let deps: any[] = []\n  let result: TResult | undefined\n\n  return depArgs => {\n    let depTime: number\n    if (opts.key && opts.debug) depTime = Date.now()\n\n    const newDeps = getDeps(depArgs)\n\n    const depsChanged =\n      newDeps.length !== deps.length ||\n      newDeps.some((dep: any, index: number) => deps[index] !== dep)\n\n    if (!depsChanged) {\n      return result!\n    }\n\n    deps = newDeps\n\n    let resultTime: number\n    if (opts.key && opts.debug) resultTime = Date.now()\n\n    result = fn(...newDeps)\n    opts?.onChange?.(result)\n\n    if (opts.key && opts.debug) {\n      if (opts?.debug()) {\n        const depEndTime = Math.round((Date.now() - depTime!) * 100) / 100\n        const resultEndTime = Math.round((Date.now() - resultTime!) * 100) / 100\n        const resultFpsPercentage = resultEndTime / 16\n\n        const pad = (str: number | string, num: number) => {\n          str = String(str)\n          while (str.length < num) {\n            str = ' ' + str\n          }\n          return str\n        }\n\n        console.info(\n          `%c⏱ ${pad(resultEndTime, 5)} /${pad(depEndTime, 5)} ms`,\n          `\n            font-size: .6rem;\n            font-weight: bold;\n            color: hsl(${Math.max(\n              0,\n              Math.min(120 - 120 * resultFpsPercentage, 120)\n            )}deg 100% 31%);`,\n          opts?.key\n        )\n      }\n    }\n\n    return result!\n  }\n}\n\nexport function getMemoOptions(\n  tableOptions: Partial<TableOptionsResolved<any>>,\n  debugLevel:\n    | 'debugAll'\n    | 'debugCells'\n    | 'debugTable'\n    | 'debugColumns'\n    | 'debugRows'\n    | 'debugHeaders',\n  key: string,\n  onChange?: (result: any) => void\n) {\n  return {\n    debug: () => tableOptions?.debugAll ?? tableOptions[debugLevel],\n    key: process.env.NODE_ENV === 'development' && key,\n    onChange,\n  }\n}\n"], "names": ["functionalUpdate", "updater", "input", "noop", "makeStateUpdater", "key", "instance", "setState", "old", "isFunction", "d", "Function", "isNumberArray", "Array", "isArray", "every", "val", "flattenBy", "arr", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flat", "recurse", "subArr", "for<PERSON>ach", "item", "push", "children", "length", "memo", "getDeps", "fn", "opts", "deps", "result", "depArgs", "depTime", "debug", "Date", "now", "newDeps", "depsChanged", "some", "dep", "index", "resultTime", "onChange", "depEndTime", "Math", "round", "resultEndTime", "resultFpsPercentage", "pad", "str", "num", "String", "console", "info", "max", "min", "getMemoOptions", "tableOptions", "debugLevel", "_tableOptions$debugAl", "debugAll", "process", "env", "NODE_ENV"], "mappings": ";;;;;;;;;;;;AA4BA;;AAOA;;AA2CA;;AAEO,SAASA,gBAAgBA,CAAIC,OAAmB,EAAEC,KAAQ,EAAK;EACpE,OAAO,OAAOD,OAAO,KAAK,UAAU,GAC/BA,OAAO,CAAqBC,KAAK,CAAC,GACnCD,OAAO,CAAA;AACb,CAAA;AAEO,SAASE,IAAIA,GAAG;AACrB;AAAA,CAAA;AAGK,SAASC,gBAAgBA,CAC9BC,GAAM,EACNC,QAAiB,EACjB;AACA,EAAA,OAAQL,OAA+B,IAAK;AACxCK,IAAAA,QAAQ,CAASC,QAAQ,CAAeC,GAAgB,IAAK;MAC7D,OAAO;AACL,QAAA,GAAGA,GAAG;QACN,CAACH,GAAG,GAAGL,gBAAgB,CAACC,OAAO,EAAGO,GAAG,CAASH,GAAG,CAAC,CAAA;OACnD,CAAA;AACH,KAAC,CAAC,CAAA;GACH,CAAA;AACH,CAAA;AAIO,SAASI,UAAUA,CAAwBC,CAAM,EAAU;EAChE,OAAOA,CAAC,YAAYC,QAAQ,CAAA;AAC9B,CAAA;AAEO,SAASC,aAAaA,CAACF,CAAM,EAAiB;AACnD,EAAA,OAAOG,KAAK,CAACC,OAAO,CAACJ,CAAC,CAAC,IAAIA,CAAC,CAACK,KAAK,CAACC,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,CAAC,CAAA;AACpE,CAAA;AAEO,SAASC,SAASA,CACvBC,GAAY,EACZC,WAAqC,EACrC;EACA,MAAMC,IAAa,GAAG,EAAE,CAAA;EAExB,MAAMC,OAAO,GAAIC,MAAe,IAAK;AACnCA,IAAAA,MAAM,CAACC,OAAO,CAACC,IAAI,IAAI;AACrBJ,MAAAA,IAAI,CAACK,IAAI,CAACD,IAAI,CAAC,CAAA;AACf,MAAA,MAAME,QAAQ,GAAGP,WAAW,CAACK,IAAI,CAAC,CAAA;AAClC,MAAA,IAAIE,QAAQ,IAAA,IAAA,IAARA,QAAQ,CAAEC,MAAM,EAAE;QACpBN,OAAO,CAACK,QAAQ,CAAC,CAAA;AACnB,OAAA;AACF,KAAC,CAAC,CAAA;GACH,CAAA;EAEDL,OAAO,CAACH,GAAG,CAAC,CAAA;AAEZ,EAAA,OAAOE,IAAI,CAAA;AACb,CAAA;AAEO,SAASQ,IAAIA,CAClBC,OAA2C,EAC3CC,EAA6C,EAC7CC,IAIC,EACgC;EACjC,IAAIC,IAAW,GAAG,EAAE,CAAA;AACpB,EAAA,IAAIC,MAA2B,CAAA;AAE/B,EAAA,OAAOC,OAAO,IAAI;AAChB,IAAA,IAAIC,OAAe,CAAA;AACnB,IAAA,IAAIJ,IAAI,CAAC1B,GAAG,IAAI0B,IAAI,CAACK,KAAK,EAAED,OAAO,GAAGE,IAAI,CAACC,GAAG,EAAE,CAAA;AAEhD,IAAA,MAAMC,OAAO,GAAGV,OAAO,CAACK,OAAO,CAAC,CAAA;IAEhC,MAAMM,WAAW,GACfD,OAAO,CAACZ,MAAM,KAAKK,IAAI,CAACL,MAAM,IAC9BY,OAAO,CAACE,IAAI,CAAC,CAACC,GAAQ,EAAEC,KAAa,KAAKX,IAAI,CAACW,KAAK,CAAC,KAAKD,GAAG,CAAC,CAAA;IAEhE,IAAI,CAACF,WAAW,EAAE;AAChB,MAAA,OAAOP,MAAM,CAAA;AACf,KAAA;AAEAD,IAAAA,IAAI,GAAGO,OAAO,CAAA;AAEd,IAAA,IAAIK,UAAkB,CAAA;AACtB,IAAA,IAAIb,IAAI,CAAC1B,GAAG,IAAI0B,IAAI,CAACK,KAAK,EAAEQ,UAAU,GAAGP,IAAI,CAACC,GAAG,EAAE,CAAA;AAEnDL,IAAAA,MAAM,GAAGH,EAAE,CAAC,GAAGS,OAAO,CAAC,CAAA;IACvBR,IAAI,IAAA,IAAA,IAAJA,IAAI,CAAEc,QAAQ,IAAA,IAAA,IAAdd,IAAI,CAAEc,QAAQ,CAAGZ,MAAM,CAAC,CAAA;AAExB,IAAA,IAAIF,IAAI,CAAC1B,GAAG,IAAI0B,IAAI,CAACK,KAAK,EAAE;AAC1B,MAAA,IAAIL,IAAI,IAAJA,IAAAA,IAAAA,IAAI,CAAEK,KAAK,EAAE,EAAE;AACjB,QAAA,MAAMU,UAAU,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACX,IAAI,CAACC,GAAG,EAAE,GAAGH,OAAQ,IAAI,GAAG,CAAC,GAAG,GAAG,CAAA;AAClE,QAAA,MAAMc,aAAa,GAAGF,IAAI,CAACC,KAAK,CAAC,CAACX,IAAI,CAACC,GAAG,EAAE,GAAGM,UAAW,IAAI,GAAG,CAAC,GAAG,GAAG,CAAA;AACxE,QAAA,MAAMM,mBAAmB,GAAGD,aAAa,GAAG,EAAE,CAAA;AAE9C,QAAA,MAAME,GAAG,GAAGA,CAACC,GAAoB,EAAEC,GAAW,KAAK;AACjDD,UAAAA,GAAG,GAAGE,MAAM,CAACF,GAAG,CAAC,CAAA;AACjB,UAAA,OAAOA,GAAG,CAACzB,MAAM,GAAG0B,GAAG,EAAE;YACvBD,GAAG,GAAG,GAAG,GAAGA,GAAG,CAAA;AACjB,WAAA;AACA,UAAA,OAAOA,GAAG,CAAA;SACX,CAAA;AAEDG,QAAAA,OAAO,CAACC,IAAI,CACV,OAAOL,GAAG,CAACF,aAAa,EAAE,CAAC,CAAC,CAAA,EAAA,EAAKE,GAAG,CAACL,UAAU,EAAE,CAAC,CAAC,KAAK,EACxD,CAAA;AACV;AACA;AACA,uBAAyBC,EAAAA,IAAI,CAACU,GAAG,CACnB,CAAC,EACDV,IAAI,CAACW,GAAG,CAAC,GAAG,GAAG,GAAG,GAAGR,mBAAmB,EAAE,GAAG,CAC/C,CAAC,CAAA,cAAA,CAAgB,EACnBnB,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAE1B,GACR,CAAC,CAAA;AACH,OAAA;AACF,KAAA;AAEA,IAAA,OAAO4B,MAAM,CAAA;GACd,CAAA;AACH,CAAA;AAEO,SAAS0B,cAAcA,CAC5BC,YAAgD,EAChDC,UAMkB,EAClBxD,GAAW,EACXwC,QAAgC,EAChC;EACA,OAAO;AACLT,IAAAA,KAAK,EAAEA,MAAA;AAAA,MAAA,IAAA0B,qBAAA,CAAA;AAAA,MAAA,OAAA,CAAAA,qBAAA,GAAMF,YAAY,IAAA,IAAA,GAAA,KAAA,CAAA,GAAZA,YAAY,CAAEG,QAAQ,KAAA,IAAA,GAAAD,qBAAA,GAAIF,YAAY,CAACC,UAAU,CAAC,CAAA;AAAA,KAAA;IAC/DxD,GAAG,EAAE2D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,IAAI7D,GAAG;AAClDwC,IAAAA,QAAAA;GACD,CAAA;AACH;;;;;;;;;;;"}